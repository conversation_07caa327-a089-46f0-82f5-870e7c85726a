<template>
  <div class="test-scene-content">
    <!-- 整合的场景管理器 -->
    <scene-manager :project-id="projectID" :scenes="scenes" :loading="loading" @save-scene="handleSaveScene"
      @delete-scene="handleDeleteScene" @copy-scene="handleCopyScene" @execute-scene="handleExecuteScene"
      @refresh="loadScenes" />
  </div>
</template>

<script>
import {
  getApiTestScenesApi,
  createApiTestSceneApi,
  updateApiTestSceneApi,
  deleteApiTestSceneApi,
  copyApiTestSceneApi,
  executeApiTestSceneApi,
  handleApiError
} from '@/api/apiTestCase'
import SceneManager from './components/scene/SceneManager.vue'

export default {
  name: 'TestSceneContent',
  components: {
    SceneManager
  },
  props: {
    projectID: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      // 场景数据（列表内容本地维护即可，视图状态交由 vuex）
      scenes: [],
      loading: false
    }
  },

  mounted() {
    this.loadScenes()
  },
  methods: {
    // 加载场景列表
    async loadScenes() {
      this.loading = true
      try {
        const response = await getApiTestScenesApi(this.projectID)

        if (response.data.code) {
          this.scenes = response.data.result || []
          // 通知TestScenePanel更新场景数据
          this.$root.$emit('scenes-loaded', this.scenes)
        } else {
          this.$message.error(response.data.message || '加载场景列表失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      } finally {
        this.loading = false
      }
    },



    // 场景操作（统一处理创建和更新）
    async handleSaveScene(sceneData, mode) {
      try {
        let response
        if (mode === 'create') {
          response = await createApiTestSceneApi(this.projectID, sceneData)
        } else {
          response = await updateApiTestSceneApi(this.projectID, sceneData.id, sceneData)
        }

        if (response.data.code) {
          this.$message.success(mode === 'create' ? '场景创建成功' : '场景更新成功')
          this.loadScenes()
        } else {
          this.$message.error(response.data.message || (mode === 'create' ? '创建场景失败' : '更新场景失败'))
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    async handleDeleteScene(scene) {
      try {
        await this.$confirm(`确定要删除场景 "${scene.name}" 吗？`, '确认删除', {
          type: 'warning'
        })

        const response = await deleteApiTestSceneApi(this.projectID, scene.id)

        if (response.data.code) {
          this.$message.success('场景删除成功')
          this.loadScenes()
        } else {
          this.$message.error(response.data.message || '删除场景失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(handleApiError(error))
        }
      }
    },

    async handleCopyScene(scene) {
      try {
        const response = await copyApiTestSceneApi(this.projectID, scene.id, {
          name: `${scene.name} - 副本`
        })
        if (response.data.code) {
          this.$message.success('场景复制成功')
          this.loadScenes()
        } else {
          this.$message.error(response.data.message || '复制场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    async handleExecuteScene(scene) {
      try {
        const response = await executeApiTestSceneApi(this.projectID, scene.id)
        if (response.data.code) {
          this.$message.success('场景执行已启动')
          // 可以跳转到执行历史页面或显示执行状态
        } else {
          this.$message.error(response.data.message || '执行场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    }
  }
}
</script>

<style scoped>
.test-scene-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.scene-list-view,
.scene-form-view {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>