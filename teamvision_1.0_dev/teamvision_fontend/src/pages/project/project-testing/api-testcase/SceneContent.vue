<template>
  <div class="scene-manager">
    <!-- 主要标签页 -->
    <el-tabs v-model="activeMainTab" type="card" class="scene-main-tabs" @tab-click="handleMainTabClick"
      @tab-remove="handleTabRemove">
      <!-- 场景列表标签页 -->
      <el-tab-pane label="全部场景" name="list">
        <template slot="label">
          <span>全部场景</span>
        </template>

        <!-- 场景列表工具栏 -->
        <div class="scene-toolbar">
          <div class="search-section">
            <el-input v-model="searchKeyword" placeholder="请过ID/名称/负责人等信息" class="search-input" @input="handleSearch">
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </div>

          <div class="filter-section">
            <span class="filter-label">场景等级:</span>
            <el-select v-model="levelFilter" placeholder="全部数据" class="filter-select" @change="handleFilter">
              <el-option label="全部数据" value=""></el-option>
              <el-option label="P0" value="P0"></el-option>
              <el-option label="P1" value="P1"></el-option>
              <el-option label="P2" value="P2"></el-option>
            </el-select>
          </div>

          <div class="action-section">
            <el-button icon="el-icon-delete" @click="clearFilters">清除</el-button>
            <el-button icon="el-icon-refresh" @click="loadScenes">刷新</el-button>
            <el-button icon="el-icon-upload2" @click="showImportDialog">导入场景</el-button>
            <el-button icon="el-icon-download" @click="exportSelectedScenes"
              :disabled="selectedScenes.length === 0">批量导出</el-button>
            <el-button type="primary" icon="el-icon-plus" @click="createScene">新建场景</el-button>
          </div>
        </div>

        <!-- 场景表格 -->
        <div class="scene-content">
          <div class="scene-table-container">
            <el-table :data="filteredScenes" v-loading="loading" stripe style="width: 100%"
              @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50"></el-table-column>

              <el-table-column prop="id" label="ID" width="80" sortable>
                <template slot-scope="scope">
                  <span class="scene-id">{{ scope.row.id }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="name" label="场景名称" min-width="200" sortable>
                <template slot-scope="scope">
                  <span class="scene-name">{{ scope.row.name }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="level" label="场景等级" width="120" sortable>
                <template slot-scope="scope">
                  <el-tag :type="getLevelTagType(scope.row.level)" class="priority-badge">
                    ⭕ {{ scope.row.level || 'P0' }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="status" label="状态" width="100" sortable>
                <template slot-scope="scope">
                  <el-tag :type="getStatusTagType(scope.row.status)" class="status-badge">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="执行结果" width="150">
                <template slot-scope="scope">
                  <el-tag :type="getResultTagType(scope.row.last_result)" class="result-badge">
                    🟢 {{ getResultText(scope.row.last_result) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="tags" label="标签" width="120">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.tags" class="tag-badge"> {{ scope.row.tags }}
                  </el-tag>
                  <span v-else>-</span>
                </template>
              </el-table-column>

              <el-table-column prop="environment" label="场景环境" width="120">
                <template slot-scope="scope">
                  <span>{{ scope.row.environment_name || 'Halo' }}</span>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="200" fixed="right">
                <template slot-scope="scope">
                  <div class="actions-cell">
                    <el-button type="text" class="action-btn edit-btn" @click="editScene(scope.row)"> 编辑
                    </el-button>
                    <el-button type="text" class="action-btn run-btn" @click="handleExecuteScene(scope.row)"> 执行
                    </el-button>
                    <el-button type="text" class="action-btn copy-btn" @click="handleCopyScene(scope.row)"> 复制
                    </el-button>
                    <el-dropdown @command="handleCommand" trigger="click">
                      <el-button type="text" class="action-btn more-btn">
                        <i class="el-icon-more"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="{ action: 'delete', scene: scope.row }">删除</el-dropdown-item>
                        <el-dropdown-item :command="{ action: 'export', scene: scope.row }">导出</el-dropdown-item>
                        <el-dropdown-item :command="{ action: 'history', scene: scope.row }">执行历史</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页 -->
          <div class="pagination-container">
            <div class="pagination-info">
              共 {{ total }} 条
            </div>
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
              layout="sizes, prev, pager, next, jumper" :total="total" class="pagination-controls">
            </el-pagination>
          </div>
        </div>
      </el-tab-pane>

      <!-- 动态场景表单标签页 -->
      <el-tab-pane v-for="tab in sceneTabs" :key="tab.name" :label="tab.label" :name="tab.name" :closable="true">
        <template slot="label">
          <span>{{ tab.label }}</span>
          <el-badge v-if="hasUnsavedChanges(tab)" is-dot class="unsaved-badge"></el-badge>
        </template>

        <!-- 场景基本信息（编辑模式显示） -->
        <div v-if="tab.mode === 'edit'" class="basic-info-section">
          <div class="basic-info-row">
            <span class="basic-info-label">{{ getStatusText(tab.sceneForm.status) }}</span>
            <span class="priority-indicator">
              <span class="priority-dot" :class="tab.sceneForm.level"></span>
              <span>{{ tab.sceneForm.level || 'P0' }}</span>
            </span>
            <span class="basic-info-value">[{{ tab.sceneForm.id }}] {{ tab.sceneForm.name }}</span>
            <el-button type="text" icon="el-icon-star-off"></el-button>
            <el-button type="text" icon="el-icon-document"></el-button>
          </div>
          <div class="basic-info-row">
            <span class="basic-info-label">标签</span>
            <span class="basic-info-value">{{ tab.sceneForm.tags || '-' }}</span>
          </div>
          <div class="basic-info-row">
            <span class="basic-info-label">描述</span>
            <span class="basic-info-value">{{ tab.sceneForm.description || '-' }}</span>
          </div>
        </div>

        <!-- 表单内容标签页 -->
        <div class="scene-form-tabs-content">
          <div class="scene-form-tabs-left">
            <div v-for="formTab in getFormTabs(tab.mode)" :key="formTab.key"
              :class="['scene-form-tab-item', { active: tab.activeTab === formTab.key }]"
              @click="switchFormTab(formTab.key, tab)">
              {{ formTab.label }}
            </div>
          </div>

          <!-- 场景表单头部操作栏 -->
          <div class="scene-form-actions">
            <div class="left-actions">
              <el-button icon="el-icon-view" title="预览"> </el-button>
              <el-dropdown @command="handleTabCommand" trigger="click">
                <el-button type="text" class="more-actions-btn">
                  <i class="el-icon-more"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="{ action: 'close', tab: tab }">关闭标签页</el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'closeOthers', tab: tab }">关闭其他</el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'closeAll' }">关闭所有</el-dropdown-item>
                  <el-dropdown-item divided :command="{ action: 'duplicate', tab: tab }">复制标签页</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <div class="right-actions">
              <el-button type="primary" @click="executeScene(tab)" :disabled="tab.mode === 'create' || tab.executing"
                :loading="tab.executing">
                {{ tab.executing ? '执行中...' : '服务端执行' }}
              </el-button>
              <el-button type="danger" v-if="tab.executing" @click="stopExecution(tab)">停止执行</el-button>
              <el-button type="success" @click="saveScene(tab)" :loading="tab.saving">
                {{ tab.saving ? '保存中...' : '保存' }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 执行进度显示 -->
        <div v-if="tab.executing" class="execution-progress">
          <div class="progress-header">
            <h4>执行进度</h4>
            <el-button type="text" @click="toggleExecutionDetails(tab)">
              {{ tab.showExecutionDetails ? '隐藏详情' : '显示详情' }}
            </el-button>
          </div>

          <el-progress :percentage="tab.executionProgress" :status="getExecutionStatus(tab)" show-text></el-progress>

          <div class="progress-info">
            <span>当前步骤: {{ tab.currentStep || '准备中...' }}</span>
            <span>已执行: {{ tab.completedSteps || 0 }} / {{ tab.totalSteps || 0 }}</span>
            <span>耗时: {{ formatDuration(tab.executionDuration) }}</span>
          </div>

          <!-- 执行详情 -->
          <div v-if="tab.showExecutionDetails" class="execution-details">
            <div class="execution-log">
              <h5>执行日志</h5>
              <div class="log-content">
                <div v-for="(log, index) in tab.executionLogs" :key="index" :class="['log-item', `log-${log.level}`]">
                  <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                  <span class="log-level">{{ log.level.toUpperCase() }}</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 表单主体 -->
        <div class="scene-form-body" v-loading="tab.loading" element-loading-text="正在加载场景详情...">
          <!-- 步骤管理 -->
          <div v-if="tab.activeTab === 'steps'" class="scene-steps-content">
            <scene-steps-manager :project-id="projectID" :scene-id="tab.sceneForm.id" :steps="tab.sceneForm.steps"
              @steps-change="(steps) => handleStepsChange(steps, tab)" />
          </div>

          <!-- 参数配置 -->
          <div v-if="tab.activeTab === 'params'" class="scene-params-content">
            <scene-params-manager :global-variables="tab.sceneForm.global_variables"
              :global-headers="tab.sceneForm.global_headers"
              @params-change="(params) => handleParamsChange(params, tab)" />
          </div>

          <!-- 前/后置脚本 -->
          <div v-if="tab.activeTab === 'scripts'" class="scene-scripts-content">
            <scene-scripts-manager :pre-script="tab.sceneForm.pre_script" :post-script="tab.sceneForm.post_script"
              @scripts-change="(scripts) => handleScriptsChange(scripts, tab)" />
          </div>

          <!-- 断言配置 -->
          <div v-if="tab.activeTab === 'assertions'" class="scene-assertions-content">
            <scene-assertions-manager :assertions="tab.sceneForm.assertions"
              @assertions-change="(assertions) => handleAssertionsChange(assertions, tab)" />
          </div>

          <!-- 执行历史 -->
          <div v-if="tab.activeTab === 'execution'" class="scene-execution-content">
            <scene-execution-history :project-id="projectID" :scene-id="tab.sceneForm.id" />
          </div>

          <!-- 变更历史 -->
          <div v-if="tab.activeTab === 'changes'" class="scene-changes-content">
            <scene-change-history :project-id="projectID" :scene-id="tab.sceneForm.id" />
          </div>

          <!-- 设置 -->
          <div v-if="tab.activeTab === 'settings'" class="scene-settings-content">
            <scene-settings-manager :settings="tab.sceneForm.settings"
              @settings-change="(settings) => handleSettingsChange(settings, tab)" />
          </div>

          <!-- 右侧表单 -->
          <div class="scene-form-sidebar">
            <el-form :model="tab.sceneForm" :rules="formRules" :ref="`sceneForm_${tab.name}`" label-width="80px">
              <el-form-item label="场景名称" prop="name" required>
                <el-input v-model="tab.sceneForm.name" placeholder="请输入场景名称"></el-input>
              </el-form-item>

              <el-form-item label="所属模块">
                <el-select v-model="tab.sceneForm.module" placeholder="未规划场景" style="width: 100%">
                  <el-option label="未规划场景" value=""></el-option>
                  <el-option label="登录模块" value="login"></el-option>
                  <el-option label="用户模块" value="user"></el-option>
                  <el-option label="文章模块" value="article"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="场景等级">
                <el-radio-group v-model="tab.sceneForm.level">
                  <el-radio label="P0">P0</el-radio>
                  <el-radio label="P1">P1</el-radio>
                  <el-radio label="P2">P2</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="场景状态">
                <el-select v-model="tab.sceneForm.status" style="width: 100%">
                  <el-option label="进行中" value="progress"></el-option>
                  <el-option label="已完成" value="completed"></el-option>
                  <el-option label="待处理" value="pending"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="标签">
                <el-input v-model="tab.sceneForm.tags" placeholder="添加标签，回车键确定"></el-input>
              </el-form-item>

              <el-form-item label="描述">
                <el-input type="textarea" v-model="tab.sceneForm.description" placeholder="请对场景进行描述"
                  :rows="4"></el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  getApiTestScenesApi,
  createApiTestSceneApi,
  updateApiTestSceneApi,
  deleteApiTestSceneApi,
  copyApiTestSceneApi,
  executeApiTestSceneApi,
  getApiTestSceneApi,
  getApiTestSceneStepsApi,
  handleApiError
} from '@/api/apiTestCase'
import SceneStepsManager from './components/scene/SceneStepsManager.vue'
import SceneParamsManager from './components/scene/SceneParamsManager.vue'
import SceneScriptsManager from './components/scene/SceneScriptsManager.vue'
import SceneAssertionsManager from './components/scene/SceneAssertionsManager.vue'
import SceneExecutionHistory from './components/scene/SceneExecutionHistory.vue'
import SceneChangeHistory from './components/scene/SceneChangeHistory.vue'
import SceneSettingsManager from './components/scene/SceneSettingsManager.vue'

export default {
  name: 'SceneContent',
  components: {
    SceneStepsManager,
    SceneParamsManager,
    SceneScriptsManager,
    SceneAssertionsManager,
    SceneExecutionHistory,
    SceneChangeHistory,
    SceneSettingsManager
  },
  props: {
    projectID: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      // 场景数据（列表内容本地维护即可，视图状态交由 vuex）
      scenes: [],
      loading: false,

      // 主标签页状态
      activeMainTab: 'list',

      // 场景表单标签页
      sceneTabs: [],
      tabCounter: 0,

      // 搜索和筛选
      searchKeyword: '',
      levelFilter: '',
      filteredScenes: [],

      // 分页
      currentPage: 1,
      pageSize: 20,
      total: 0,

      // 选中项
      selectedScenes: [],

      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入场景名称', trigger: 'blur' },
          { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
        ]
      },

      // 导入导出相关
      showImportDialogVisible: false,
      showExportDialogVisible: false,
      importActiveTab: 'file',
      importing: false,
      exporting: false,

      // 文件上传
      fileList: [],
      uploadUrl: '/api/scenes/import',
      uploadHeaders: {},
      uploadData: {},

      // 导入选项
      importOptions: {
        overwrite: false,
        keepId: false,
        updateSteps: true
      },

      // 模板相关
      selectedTemplateCategory: 'all',
      selectedTemplates: [],
      sceneTemplates: [
        {
          id: 1,
          name: 'API基础测试模板',
          category: 'api',
          description: '包含常用的API测试步骤，适用于RESTful API测试',
          stepCount: 5,
          useCount: 128
        },
        {
          id: 2,
          name: '性能测试模板',
          category: 'performance',
          description: '包含性能测试相关的步骤配置，支持并发和压力测试',
          stepCount: 8,
          useCount: 45
        },
        {
          id: 3,
          name: '集成测试模板',
          category: 'integration',
          description: '多系统集成测试模板，包含数据准备和清理步骤',
          stepCount: 12,
          useCount: 67
        }
      ],

      // 导出选项
      exportOptions: {
        format: 'json',
        includes: ['steps', 'params', 'scripts', 'assertions', 'settings'],
        includeHistory: false,
        compress: false
      },

      // 跟踪标签页的修改状态
      tabModifications: new Map(), // 存储每个标签页的修改记录
    }
  },
  computed: {
    ...mapState('usercenter', ["userInfo"]),

    // 筛选后的模板
    filteredTemplates() {
      if (this.selectedTemplateCategory === 'all') {
        return this.sceneTemplates
      }
      return this.sceneTemplates.filter(template => template.category === this.selectedTemplateCategory)
    }
  },
  watch: {
    scenes: {
      handler() {
        this.filterScenes()
      },
      immediate: true
    }
  },
  mounted() {
    this.loadScenes()
  },
  methods: {
    // 加载场景列表
    async loadScenes() {
      this.loading = true
      try {
        const response = await getApiTestScenesApi(this.projectID)

        if (response.data.code) {
          this.scenes = response.data.result || []
          // 通知TestScenePanel更新场景数据
          this.$root.$emit('scenes-loaded', this.scenes)
        } else {
          this.$message.error(response.data.message || '加载场景列表失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      } finally {
        this.loading = false
      }
    },

    // 主标签页点击处理
    handleMainTabClick(tab) {
      // 如果点击的是场景表单标签页，需要处理相关逻辑
      if (tab.name !== 'list') {
        // 可以在这里添加标签页切换的逻辑
      }
    },

    // 标签页关闭处理
    handleTabRemove(tabName) {
      const tabIndex = this.sceneTabs.findIndex(tab => tab.name === tabName)
      if (tabIndex === -1) return

      const tab = this.sceneTabs[tabIndex]
      this.confirmCloseTab(tab, tabIndex, tabName)
    },

    // 标签页操作命令处理
    handleTabCommand(command) {
      const { action, tab } = command
      switch (action) {
        case 'close':
          this.handleTabRemove(tab.name)
          break
        case 'closeOthers':
          this.closeOtherTabs(tab.name)
          break
        case 'closeAll':
          this.closeAllTabs()
          break
        case 'duplicate':
          this.duplicateTab(tab)
          break
      }
    },

    // 确认关闭标签页
    async confirmCloseTab(tab, tabIndex, tabName) {
      // 如果标签页正在执行，先提示停止
      if (tab.executing) {
        try {
          await this.$confirm('当前场景正在执行，关闭标签页将停止执行，确定要关闭吗？', '提示', {
            confirmButtonText: '确定关闭',
            cancelButtonText: '取消',
            type: 'warning'
          })
          this.stopExecution(tab)
        } catch {
          return // 用户取消
        }
      }

      // 检查是否有未保存的修改
      if (this.hasUnsavedChanges(tab)) {
        try {
          const action = await this.$confirm('当前标签页有未保存的修改，确定要关闭吗？', '提示', {
            confirmButtonText: '保存并关闭',
            cancelButtonText: '直接关闭',
            type: 'warning',
            distinguishCancelAndClose: true,
            showClose: true
          }).catch(action => {
            if (action === 'cancel') {
              return 'close' // 直接关闭
            } else {
              throw action // 用户点击了 X，取消操作
            }
          })

          if (action === 'confirm') {
            // 保存并关闭
            await this.saveSceneBeforeClose(tab)
          }
        } catch {
          return // 用户取消
        }
      }

      this.closeTab(tabIndex, tabName)
    },

    // 保存场景后关闭
    async saveSceneBeforeClose(tab) {
      try {
        tab.saving = true
        await this.saveScene(tab)
        this.$message.success('场景已保存')
      } catch (error) {
        this.$message.error(`保存失败: ${error.message}`)
        throw error
      } finally {
        tab.saving = false
      }
    },

    // 关闭标签页
    closeTab(tabIndex, tabName) {
      const tab = this.sceneTabs[tabIndex]

      try {
        // 清理定时器
        if (tab.executionTimer) {
          clearInterval(tab.executionTimer)
        }

        // 清理修改记录
        this.tabModifications.delete(tabName)

        // 移除标签页
        this.sceneTabs.splice(tabIndex, 1)

        // 如果关闭的是当前活动标签页，需要切换到其他标签页
        if (this.activeMainTab === tabName) {
          if (this.sceneTabs.length > 0) {
            // 智能选择下一个活动标签页
            const nextIndex = Math.min(tabIndex, this.sceneTabs.length - 1)
            this.activeMainTab = this.sceneTabs[nextIndex].name
          } else {
            // 没有其他标签页，切换到场景列表
            this.activeMainTab = 'list'
          }
        }

        this.$message({
          message: `标签页 "${tab.label}" 已关闭`,
          type: 'success',
          duration: 2000
        })
      } catch (error) {
        console.error('关闭标签页失败:', error)
        this.$message.error('关闭标签页失败')
      }
    },

    // 场景操作（统一处理创建和更新）
    async handleSaveScene(sceneData, mode) {
      try {
        let response
        if (mode === 'create') {
          response = await createApiTestSceneApi(this.projectID, sceneData)
        } else {
          response = await updateApiTestSceneApi(this.projectID, sceneData.id, sceneData)
        }

        if (response.data.code) {
          this.$message.success(mode === 'create' ? '场景创建成功' : '场景更新成功')
          this.loadScenes()
        } else {
          this.$message.error(response.data.message || (mode === 'create' ? '创建场景失败' : '更新场景失败'))
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    async handleDeleteScene(scene) {
      try {
        await this.$confirm(`确定要删除场景 "${scene.name}" 吗？`, '确认删除', {
          type: 'warning'
        })

        const response = await deleteApiTestSceneApi(this.projectID, scene.id)

        if (response.data.code) {
          this.$message.success('场景删除成功')
          this.loadScenes()
        } else {
          this.$message.error(response.data.message || '删除场景失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(handleApiError(error))
        }
      }
    },

    async handleCopyScene(scene) {
      try {
        const response = await copyApiTestSceneApi(this.projectID, scene.id, {
          name: `${scene.name} - 副本`
        })
        if (response.data.code) {
          this.$message.success('场景复制成功')
          this.loadScenes()
        } else {
          this.$message.error(response.data.message || '复制场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    async handleExecuteScene(scene) {
      try {
        const response = await executeApiTestSceneApi(this.projectID, scene.id)
        if (response.data.code) {
          this.$message.success('场景执行已启动')
          // 可以跳转到执行历史页面或显示执行状态
        } else {
          this.$message.error(response.data.message || '执行场景失败')
        }
      } catch (error) {
        this.$message.error(handleApiError(error))
      }
    },

    // 创建新场景
    createScene() {
      const tabName = `create_${++this.tabCounter}`
      const newTab = {
        name: tabName,
        label: '新建场景',
        mode: 'create',
        activeTab: 'steps',
        loading: false,
        saving: false,
        // 执行状态
        executing: false,
        executionProgress: 0,
        currentStep: '',
        completedSteps: 0,
        totalSteps: 0,
        executionDuration: 0,
        executionStartTime: null,
        showExecutionDetails: false,
        executionLogs: [],
        executionTimer: null,
        sceneForm: {
          name: '',
          module: '',
          level: 'P0',
          status: 'progress',
          tags: '',
          description: '',
          steps: [],
          global_variables: {},
          global_headers: {},
          pre_script: '',
          post_script: '',
          assertions: [],
          settings: {},
          project_id: this.projectID,
          creator: this.userInfo.id,
        }
      }

      this.sceneTabs.push(newTab)
      this.activeMainTab = tabName
    },

    // 编辑场景
    async editScene(scene) {
      // 检查是否已经打开了该场景的编辑标签页
      const existingTab = this.sceneTabs.find(tab =>
        tab.mode === 'edit' && tab.sceneForm.id === scene.id
      )

      if (existingTab) {
        this.activeMainTab = existingTab.name
        this.$message.info('该场景已在编辑中')
        return
      }

      const tabName = `edit_${scene.id}_${++this.tabCounter}`

      // 创建初始标签页
      const newTab = {
        name: tabName,
        label: scene.name,
        mode: 'edit',
        activeTab: 'steps',
        loading: true,
        saving: false,
        // 执行状态
        executing: false,
        executionProgress: 0,
        currentStep: '',
        completedSteps: 0,
        totalSteps: 0,
        executionDuration: 0,
        executionStartTime: null,
        showExecutionDetails: false,
        executionLogs: [],
        executionTimer: null,
        sceneForm: { ...scene }
      }

      this.sceneTabs.push(newTab)
      this.activeMainTab = tabName

      // 从后端获取完整的场景详情
      try {
        await this.loadSceneDetail(newTab, scene.id)
      } catch (error) {
        console.error('加载场景详情失败:', error)
        this.$message.error(`加载场景详情失败: ${error.message}`)
        // 如果加载失败，移除标签页
        const tabIndex = this.sceneTabs.findIndex(tab => tab.name === tabName)
        if (tabIndex > -1) {
          this.sceneTabs.splice(tabIndex, 1)
          this.activeMainTab = 'list'
        }
      }
    },

    // 加载场景详情
    async loadSceneDetail(tab, sceneId) {
      try {
        tab.loading = true

        // 并行获取场景详情和步骤列表
        const [sceneResponse, stepsResponse] = await Promise.all([
          getApiTestSceneApi(this.projectID, sceneId),
          getApiTestSceneStepsApi(this.projectID, sceneId)
        ])

        // 处理场景详情响应
        if (sceneResponse.data && sceneResponse.data.code === 200) {
          const sceneDetail = sceneResponse.data.result

          // 更新场景表单数据
          tab.sceneForm = {
            ...tab.sceneForm,
            ...sceneDetail,
            // 确保必要字段存在
            steps: [],
            global_variables: sceneDetail.global_variables || {},
            global_headers: sceneDetail.global_headers || {},
            pre_script: sceneDetail.pre_script || '',
            post_script: sceneDetail.post_script || '',
            assertions: sceneDetail.assertions || [],
            settings: sceneDetail.settings || {}
          }

          // 更新标签页标题
          tab.label = sceneDetail.name
        } else {
          throw new Error(sceneResponse.data?.message || '获取场景详情失败')
        }

        // 处理步骤列表响应
        if (stepsResponse.data && stepsResponse.data.code === 200) {
          const steps = stepsResponse.data.result || []
          tab.sceneForm.steps = steps
          tab.totalSteps = steps.length
        } else {
          console.warn('获取场景步骤失败:', stepsResponse.data?.message)
          tab.sceneForm.steps = []
        }

        this.$message.success('场景详情加载完成')

      } catch (error) {
        console.error('加载场景详情失败:', error)
        throw new Error(handleApiError(error))
      } finally {
        tab.loading = false
      }
    },

    // 保存场景
    async saveScene(tab) {
      // 验证表单
      if (!tab.sceneForm.name) {
        this.$message.error('请输入场景名称')
        return
      }

      tab.sceneForm.creator = this.userInfo.id
      tab.sceneForm.project_id = this.projectID
      tab.saving = true

      try {
        await this.handleSaveScene(tab.sceneForm, tab.mode)

        // 保存成功后清除修改标记
        this.clearTabModification(tab.name)

        // 如果是新建场景，保存后切换为编辑模式
        if (tab.mode === 'create') {
          tab.mode = 'edit'
        }

        this.$message.success('场景保存成功')
      } catch (error) {
        this.$message.error(`保存失败: ${error.message}`)
        throw error
      } finally {
        tab.saving = false
      }
    },

    // 执行场景
    async executeScene(tab) {
      if (tab.mode === 'edit' && tab.sceneForm.id) {
        try {
          // 初始化执行状态
          tab.executing = true
          tab.executionProgress = 0
          tab.currentStep = '准备执行...'
          tab.completedSteps = 0
          tab.totalSteps = tab.sceneForm.steps ? tab.sceneForm.steps.length : 0
          tab.executionDuration = 0
          tab.executionStartTime = Date.now()
          tab.executionLogs = []

          // 添加开始执行日志
          this.addExecutionLog(tab, 'info', '开始执行场景')

          // 启动计时器
          this.startExecutionTimer(tab)

          // 调用API执行场景
          const response = await executeApiTestSceneApi(this.projectID, tab.sceneForm.id)

          if (response.data.code) {
            // 模拟执行进度更新
            this.simulateExecution(tab, response.data)
          } else {
            throw new Error(response.data.message || '执行失败')
          }

        } catch (error) {
          this.addExecutionLog(tab, 'error', `执行失败: ${error.message}`)
          this.stopExecutionTimer(tab)
          tab.executing = false
          this.$message.error(`场景执行失败: ${error.message}`)
        }
      }
    },

    // 停止执行
    stopExecution(tab) {
      this.addExecutionLog(tab, 'warning', '用户手动停止执行')
      this.stopExecutionTimer(tab)
      tab.executing = false
      tab.currentStep = '已停止'
      this.$message.warning('场景执行已停止')
    },

    // 切换执行详情显示
    toggleExecutionDetails(tab) {
      tab.showExecutionDetails = !tab.showExecutionDetails
    },

    // 获取执行状态
    getExecutionStatus(tab) {
      if (tab.executionProgress === 100) {
        return 'success'
      } else if (tab.executing) {
        return null // 正常进行中
      } else {
        return 'exception'
      }
    },

    // 启动执行计时器
    startExecutionTimer(tab) {
      tab.executionTimer = setInterval(() => {
        if (tab.executionStartTime) {
          tab.executionDuration = Date.now() - tab.executionStartTime
        }
      }, 1000)
    },

    // 停止执行计时器
    stopExecutionTimer(tab) {
      if (tab.executionTimer) {
        clearInterval(tab.executionTimer)
        tab.executionTimer = null
      }
    },

    // 添加执行日志
    addExecutionLog(tab, level, message) {
      tab.executionLogs.push({
        timestamp: Date.now(),
        level: level,
        message: message
      })

      // 限制日志数量
      if (tab.executionLogs.length > 100) {
        tab.executionLogs.shift()
      }
    },

    // 模拟执行过程
    simulateExecution(tab, executionData) {
      let currentStep = 0
      const steps = tab.sceneForm.steps || []

      const executeStep = () => {
        if (currentStep < steps.length && tab.executing) {
          const step = steps[currentStep]
          tab.currentStep = step.step_name || `步骤 ${currentStep + 1}`
          tab.completedSteps = currentStep
          tab.executionProgress = Math.round((currentStep / steps.length) * 100)

          this.addExecutionLog(tab, 'info', `执行步骤: ${tab.currentStep}`)

          // 模拟步骤执行时间
          setTimeout(() => {
            currentStep++
            if (currentStep >= steps.length) {
              // 执行完成
              tab.executionProgress = 100
              tab.completedSteps = steps.length
              tab.currentStep = '执行完成'
              tab.executing = false
              this.stopExecutionTimer(tab)
              this.addExecutionLog(tab, 'success', '场景执行完成')
              this.$message.success('场景执行完成')
            } else {
              executeStep()
            }
          }, Math.random() * 2000 + 1000) // 1-3秒随机执行时间
        }
      }

      executeStep()
    },

    // 格式化时间
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    },

    // 格式化持续时间
    formatDuration(duration) {
      const seconds = Math.floor(duration / 1000)
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60

      if (minutes > 0) {
        return `${minutes}分${remainingSeconds}秒`
      } else {
        return `${remainingSeconds}秒`
      }
    },

    // 搜索处理
    handleSearch() {
      this.filterScenes()
    },

    // 筛选处理
    handleFilter() {
      this.filterScenes()
    },

    // 筛选场景
    filterScenes() {
      let filtered = [...this.scenes]

      // 关键词搜索
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(scene =>
          scene.name.toLowerCase().includes(keyword) ||
          scene.id.toString().includes(keyword) ||
          (scene.description && scene.description.toLowerCase().includes(keyword))
        )
      }

      // 等级筛选
      if (this.levelFilter) {
        filtered = filtered.filter(scene => scene.level === this.levelFilter)
      }

      this.filteredScenes = filtered
      this.total = filtered.length
    },

    // 清除筛选
    clearFilters() {
      this.searchKeyword = ''
      this.levelFilter = ''
      this.filterScenes()
    },

    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.handleCurrentChange(1)
    },

    handleCurrentChange(val) {
      this.currentPage = val
    },

    // 选择处理
    handleSelectionChange(selection) {
      this.selectedScenes = selection
    },

    // 下拉菜单命令处理
    handleCommand(command) {
      const { action, scene } = command
      switch (action) {
        case 'delete':
          this.handleDeleteScene(scene)
          break
        case 'export':
          this.exportScene(scene)
          break
        case 'history':
          this.viewHistory(scene)
          break
      }
    },

    // 导出场景
    exportScene(scene) {
      this.selectedScenes = [scene]
      this.showExportDialog()
    },

    // 显示导入对话框
    showImportDialog() {
      this.showImportDialogVisible = true
      this.fileList = []
      this.selectedTemplates = []
      this.importActiveTab = 'file'
    },

    // 显示导出对话框
    showExportDialog() {
      this.showExportDialogVisible = true
      this.exportOptions = {
        format: 'json',
        includes: ['steps', 'params', 'scripts', 'assertions', 'settings'],
        includeHistory: false,
        compress: false
      }
    },

    // 批量导出选中场景
    exportSelectedScenes() {
      if (this.selectedScenes.length === 0) {
        this.$message.warning('请先选择要导出的场景')
        return
      }
      this.showExportDialog()
    },

    // 查看执行历史
    viewHistory(scene) {
      this.$message.info(`查看场景 "${scene.name}" 执行历史功能开发中`)
    },

    // 场景表单相关方法
    // 处理步骤变更
    handleStepsChange(steps, tab) {
      tab.sceneForm.steps = steps
      this.markTabAsModified(tab.name)
    },

    // 处理参数变更
    handleParamsChange(params, tab) {
      tab.sceneForm.global_variables = params.variables
      tab.sceneForm.global_headers = params.headers
      this.markTabAsModified(tab.name)
    },

    // 处理脚本变更
    handleScriptsChange(scripts, tab) {
      tab.sceneForm.pre_script = scripts.preScript
      tab.sceneForm.post_script = scripts.postScript
      this.markTabAsModified(tab.name)
    },

    // 处理断言变更
    handleAssertionsChange(assertions, tab) {
      tab.sceneForm.assertions = assertions
      this.markTabAsModified(tab.name)
    },

    // 处理设置变更
    handleSettingsChange(settings, tab) {
      tab.sceneForm.settings = settings
      this.markTabAsModified(tab.name)
    },

    // 切换场景表单内部标签页
    switchFormTab(tabKey, tab) {
      tab.activeTab = tabKey
    },

    // 获取表单标签页配置
    getFormTabs(mode) {
      const baseTabs = [
        { key: 'steps', label: '步骤' },
        { key: 'params', label: '参数' },
        { key: 'scripts', label: '前/后置' },
        { key: 'assertions', label: '断言' },
        { key: 'settings', label: '设置' }
      ]

      if (mode === 'edit') {
        baseTabs.splice(4, 0,
          { key: 'execution', label: '执行历史' },
          { key: 'changes', label: '变更历史' }
        )
      }

      return baseTabs
    },

    // 检查是否有未保存的修改
    hasUnsavedChanges(tab) {
      if (!tab) return false

      // 检查修改记录
      if (this.tabModifications.has(tab.name)) {
        return true
      }

      // 新建模式下，如果有内容就认为有修改
      if (tab.mode === 'create') {
        return !!(tab.sceneForm.name ||
          tab.sceneForm.description ||
          (tab.sceneForm.steps && tab.sceneForm.steps.length > 0))
      }

      return false
    },

    // 标记标签页为已修改
    markTabAsModified(tabName) {
      this.tabModifications.set(tabName, true)
    },

    // 清除标签页的修改标记
    clearTabModification(tabName) {
      this.tabModifications.delete(tabName)
    },

    // 获取等级标签类型
    getLevelTagType(level) {
      const typeMap = {
        'P0': 'danger',
        'P1': 'warning',
        'P2': 'info'
      }
      return typeMap[level] || 'danger'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'progress': 'warning',
        'completed': 'success',
        'pending': 'info'
      }
      return typeMap[status] || 'warning'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'progress': '进行中',
        'completed': '已完成',
        'pending': '待处理'
      }
      return textMap[status] || '进行中'
    },

    // 获取结果标签类型
    getResultTagType(result) {
      const typeMap = {
        'success': 'success',
        'failed': 'danger',
        'running': 'warning'
      }
      return typeMap[result] || 'success'
    },

    // 获取结果文本
    getResultText(result) {
      const textMap = {
        'success': '成功',
        'failed': '失败',
        'running': '运行中'
      }
      return textMap[result] || '成功'
    }
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    this.sceneTabs.forEach(tab => {
      if (tab.executionTimer) {
        clearInterval(tab.executionTimer)
      }
    })
    this.tabModifications.clear()
  }
}
</script>

<style scoped>
.scene-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

/* 主标签页样式 */
.scene-main-tabs {
  height: 100%;
}

.scene-main-tabs>>>.el-tabs__content {
  height: calc(100% - 40px);
  overflow: hidden;
}

.scene-main-tabs>>>.el-tab-pane {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 场景工具栏 */
.scene-toolbar {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  gap: 16px;
  flex-wrap: wrap;
}

.search-section {
  flex: 1;
  min-width: 200px;
}

.search-input {
  max-width: 300px;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.filter-select {
  width: 120px;
}

.action-section {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 场景内容区域 */
.scene-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.scene-table-container {
  flex: 1;
  overflow: auto;
}

/* 表格样式 */
.scene-id {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #666;
}

.scene-name {
  font-weight: 500;
  color: #333;
}

.priority-badge,
.status-badge,
.result-badge,
.tag-badge {
  font-size: 12px;
}

.actions-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  padding: 4px 8px;
  font-size: 12px;
}

.edit-btn {
  color: #409eff;
}

.run-btn {
  color: #67c23a;
}

.copy-btn {
  color: #e6a23c;
}

.more-btn {
  color: #909399;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #e9ecef;
  background: #fff;
}

.pagination-info {
  font-size: 14px;
  color: #666;
}

/* 场景表单相关样式 */
.basic-info-section {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.basic-info-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.basic-info-row:last-child {
  margin-bottom: 0;
}

.basic-info-label {
  font-size: 12px;
  color: #666;
  min-width: 60px;
}

.basic-info-value {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.priority-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.priority-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ccc;
}

.priority-dot.P0 {
  background: #f56c6c;
}

.priority-dot.P1 {
  background: #e6a23c;
}

.priority-dot.P2 {
  background: #909399;
}

/* 表单标签页 */
.scene-form-tabs-content {
  display: flex;
  height: calc(100% - 120px);
}

.scene-form-tabs-left {
  width: 120px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
}

.scene-form-tab-item {
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  border-bottom: 1px solid #e9ecef;
  transition: all 0.2s;
}

.scene-form-tab-item:hover {
  background: #e9ecef;
  color: #333;
}

.scene-form-tab-item.active {
  background: #409eff;
  color: white;
}

/* 表单操作栏 */
.scene-form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #e9ecef;
}

.left-actions,
.right-actions {
  display: flex;
  gap: 8px;
}

.more-actions-btn {
  color: #909399;
}

/* 执行进度 */
.execution-progress {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.progress-info {
  display: flex;
  gap: 24px;
  margin-top: 12px;
  font-size: 14px;
  color: #666;
}

.execution-details {
  margin-top: 16px;
}

.execution-log {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.execution-log h5 {
  margin: 0;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
  color: #333;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  font-size: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #999;
  min-width: 80px;
}

.log-level {
  min-width: 50px;
  font-weight: 500;
}

.log-message {
  flex: 1;
  color: #333;
}

.log-info .log-level {
  color: #409eff;
}

.log-success .log-level {
  color: #67c23a;
}

.log-warning .log-level {
  color: #e6a23c;
}

.log-error .log-level {
  color: #f56c6c;
}

/* 表单主体 */
.scene-form-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.scene-steps-content,
.scene-params-content,
.scene-scripts-content,
.scene-assertions-content,
.scene-execution-content,
.scene-changes-content,
.scene-settings-content {
  flex: 1;
  overflow: auto;
  padding: 16px;
}

.scene-form-sidebar {
  width: 300px;
  background: #f8f9fa;
  border-left: 1px solid #e9ecef;
  padding: 16px;
  overflow-y: auto;
}

/* 未保存标记 */
.unsaved-badge {
  margin-left: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .scene-form-sidebar {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .scene-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-section,
  .filter-section,
  .action-section {
    width: 100%;
  }

  .action-section {
    justify-content: center;
  }

  .scene-form-tabs-content {
    flex-direction: column;
  }

  .scene-form-tabs-left {
    width: 100%;
    height: 50px;
    flex-direction: row;
    overflow-x: auto;
  }

  .scene-form-tab-item {
    white-space: nowrap;
    border-right: 1px solid #e9ecef;
    border-bottom: none;
  }

  .scene-form-body {
    flex-direction: column;
  }

  .scene-form-sidebar {
    width: 100%;
    height: 200px;
    border-left: none;
    border-top: 1px solid #e9ecef;
  }
}
</style>
