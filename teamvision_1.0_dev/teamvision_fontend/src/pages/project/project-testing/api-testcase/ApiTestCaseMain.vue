<template>
  <div class="main-work-area">
    <el-container>
      <!-- 左侧功能面板 -->
      <el-aside :width="leftPanelWidth + 'px'" class="left-panel">
        <!-- Collections 选项卡 -->
        <collections-panel v-if="activeNavTab === 'collections'" :projectID="projectID" />

        <!-- Environments 选项卡 -->
        <environments-panel v-if="activeNavTab === 'environments'" :projectID="projectID" ref="environmentsPanel" />

        <!-- History 选项卡 -->
        <history-panel v-if="activeNavTab === 'history'" :projectID="projectID"
          @update-request="updateCurrentRequest" />

        <!-- Code snippet 选项卡 -->
        <code-snippet-panel v-if="activeNavTab === 'code'" :current-request="currentRequest" :projectID="projectID" />

        <!-- Scenes 选项卡 -->
        <test-scene-panel v-if="activeNavTab === 'scenes'" :projectID="projectID" />
      </el-aside>

      <!-- 右侧主工作区 -->
      <el-main class="right-panel">
        <!-- API测试用例内容 -->
        <collections-content v-if="activeNavTab !== 'scenes'" :projectID="projectID" :current-request="currentRequest"
          @update-request="updateCurrentRequest" />

        <!-- 测试场景内容 -->
        <test-scene-content v-if="activeNavTab === 'scenes'" :projectID="projectID" />
      </el-main>
    </el-container>
  </div>
</template>

<script>
import CollectionsContent from "./CollectionsContent.vue";
import CollectionsPanel from "./components/CollectionsPanel.vue";
import EnvironmentsPanel from "./components/EnvironmentsPanel.vue";
import HistoryPanel from "./components/HistoryPanel.vue";
import CodeSnippetPanel from "./components/CodeSnippetPanel.vue";
import TestScenePanel from "./components/TestScenePanel.vue";
import TestSceneContent from "./SceneContent.vue";

export default {
  name: "ApiTestCaseMain",
  props: {
    projectID: {
      type: Number,
      required: true
    },
    activeNavTab: {
      type: String,
      required: true
    },
    currentRequest: {
      type: Object,
      required: true
    }
  },
  components: {
    CollectionsContent,
    CollectionsPanel,
    EnvironmentsPanel,
    HistoryPanel,
    CodeSnippetPanel,
    TestScenePanel,
    TestSceneContent
  },
  data() {
    return {
      leftPanelWidth: 280
    }
  },
  methods: {
    updateCurrentRequest(requestData) {
      this.$emit('update-request', requestData);
    }
  }
}
</script>

<style scoped>
.main-work-area {
  height: 100%;
  background: white;
}

.main-work-area>>>.el-container {
  height: 100%;
}

/* 左侧功能面板 */
.left-panel {
  background: white;
  border-right: 1px solid #e0e0e0;
  transition: width 0.3s ease;
}

.left-panel>>>.el-aside {
  background: white;
}

/* 右侧主工作区 */
.right-panel {
  padding: 0;
  background: white;
}

.right-panel>>>.el-main {
  padding: 0;
  background: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .left-panel {
    width: 100% !important;
    height: 200px;
  }
}
</style>
